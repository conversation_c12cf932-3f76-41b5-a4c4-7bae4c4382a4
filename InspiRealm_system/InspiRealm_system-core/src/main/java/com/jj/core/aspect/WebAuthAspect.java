package com.jj.core.aspect;

import com.jj.common.exception.BizException;
import com.jj.core.annotation.WebAuth;
import com.jj.dao.mapper.UserSessionMapper;
import com.jj.dao.model.user.UserSession;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;

/**
 * Web端权限验证切面
 *
 * <AUTHOR>
 * @date 2025-01-05
 */
@Aspect
@Component
public class WebAuthAspect {

    private static final Logger logger = LoggerFactory.getLogger(WebAuthAspect.class);

    @Autowired
    private UserSessionMapper userSessionMapper;

    @Around("@annotation(webAuth)")
    public Object around(ProceedingJoinPoint joinPoint, WebAuth webAuth) throws Throwable {
        logger.debug("Web权限验证开始: {}", webAuth.value());

        try {
            // 获取当前请求
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes == null) {
                throw new BizException("无法获取请求上下文");
            }

            HttpServletRequest request = attributes.getRequest();

            // 获取Authorization头
            String authorization = request.getHeader("Authorization");
            if (!StringUtils.hasText(authorization)) {
                throw new BizException("未提供访问令牌");
            }

            // 解析Bearer Token
            String accessToken = null;
            if (authorization.startsWith("Bearer ")) {
                accessToken = authorization.substring(7);
            } else {
                accessToken = authorization;
            }

            if (!StringUtils.hasText(accessToken)) {
                throw new BizException("访问令牌格式错误");
            }

            // 验证Token
            UserSession session = userSessionMapper.selectByAccessToken(accessToken);
            if (session == null) {
                throw new BizException("访问令牌无效");
            }

            // 检查会话状态
            if (session.getStatus() != 1) {
                throw new BizException("会话已失效");
            }

            // 检查是否过期
            if (session.getExpiresAt().before(new Date())) {
                throw new BizException("访问令牌已过期");
            }

            // 更新最后活跃时间
            userSessionMapper.updateLastActiveTime(session.getSessionId());

            // 将用户信息存储到请求属性中
            request.setAttribute("currentUserId", session.getUserId());
            request.setAttribute("currentSessionId", session.getSessionId());

            logger.debug("Web权限验证通过，用户ID: {}", session.getUserId());

            // 执行目标方法
            return joinPoint.proceed();

        } catch (BizException e) {
            logger.warn("Web权限验证失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            logger.error("Web权限验证异常", e);
            throw new BizException("权限验证失败");
        }
    }
}