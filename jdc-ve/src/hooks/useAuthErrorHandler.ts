import { useCallback } from 'react';
import { AuthResponseResult, AuthStatusCode } from '@/types';
import { AuthErrorHandler } from '@/lib/auth-error-handler';

/**
 * 认证错误处理Hook
 * 提供统一的认证错误处理逻辑和通知功能
 */
export const useAuthErrorHandler = () => {
  
  /**
   * 显示通知的函数（可以根据项目使用的UI库进行调整）
   * 这里提供一个基础实现，实际项目中可以替换为具体的通知组件
   */
  const showNotification = useCallback((message: string, type: 'error' | 'warning' | 'info') => {
    // 使用浏览器原生通知作为fallback
    // 实际项目中应该替换为具体的通知组件，如 toast、notification 等
    console.log(`[${type.toUpperCase()}] ${message}`);
    
    // 示例：如果使用了某个通知库，可以这样调用
    // toast[type](message);
    // notification[type]({ message });
    
    // 临时使用alert作为演示（生产环境中应该移除）
    if (type === 'error') {
      alert(`错误: ${message}`);
    } else if (type === 'warning') {
      console.warn(message);
    } else {
      console.info(message);
    }
  }, []);

  /**
   * 处理认证错误
   */
  const handleAuthError = useCallback((authResponse: AuthResponseResult) => {
    AuthErrorHandler.handleAuthError(authResponse, showNotification);
  }, [showNotification]);

  /**
   * 处理API错误，自动检测认证错误
   */
  const handleApiError = useCallback((error: any) => {
    // 检查是否为认证错误
    if (error.authResponse) {
      handleAuthError(error.authResponse);
      return;
    }

    // 检查是否有认证状态码
    if (error.authCode) {
      const authResponse: AuthResponseResult = {
        authCode: error.authCode,
        authMessage: error.message || '认证失败',
        requireReLogin: error.requireReLogin || false,
        authSuccess: false,
        timestamp: Date.now(),
      };
      handleAuthError(authResponse);
      return;
    }

    // 处理特定的HTTP状态码
    if (error.response?.status) {
      const status = error.response.status;
      let authCode: number;
      let message: string;
      let requireReLogin = false;

      switch (status) {
        case 401:
          authCode = AuthStatusCode.AUTH_FAILED;
          message = '认证失败，请重新登录';
          requireReLogin = true;
          break;
        case 403:
          authCode = AuthStatusCode.PERMISSION_DENIED;
          message = '权限不足，无法访问该资源';
          break;
        default:
          // 非认证相关错误，显示通用错误信息
          showNotification(error.message || '请求失败', 'error');
          return;
      }

      const authResponse: AuthResponseResult = {
        authCode,
        authMessage: message,
        requireReLogin,
        authSuccess: false,
        timestamp: Date.now(),
      };
      handleAuthError(authResponse);
    } else {
      // 网络错误或其他错误
      showNotification(error.message || '网络错误，请检查网络连接', 'error');
    }
  }, [handleAuthError, showNotification]);

  /**
   * 检查是否为认证错误
   */
  const isAuthError = useCallback((error: any): boolean => {
    return !!(error.authResponse || error.authCode || 
             (error.response?.status >= 401 && error.response?.status <= 403));
  }, []);

  /**
   * 检查是否需要重新登录
   */
  const requiresReLogin = useCallback((error: any): boolean => {
    if (error.authResponse) {
      return error.authResponse.requireReLogin;
    }
    if (error.requireReLogin !== undefined) {
      return error.requireReLogin;
    }
    if (error.authCode) {
      return AuthErrorHandler.requiresReLogin(error.authCode);
    }
    return error.response?.status === 401;
  }, []);

  /**
   * 获取认证错误信息
   */
  const getAuthErrorMessage = useCallback((error: any): string => {
    if (error.authResponse) {
      return error.authResponse.authMessage;
    }
    if (error.authCode) {
      return error.message || '认证失败';
    }
    if (error.response?.status === 401) {
      return '认证失败，请重新登录';
    }
    if (error.response?.status === 403) {
      return '权限不足，无法访问该资源';
    }
    return error.message || '未知错误';
  }, []);

  return {
    handleAuthError,
    handleApiError,
    isAuthError,
    requiresReLogin,
    getAuthErrorMessage,
    showNotification,
  };
};

/**
 * 认证错误处理的便捷Hook
 * 提供简化的API错误处理
 */
export const useApiErrorHandler = () => {
  const { handleApiError, isAuthError, requiresReLogin, getAuthErrorMessage } = useAuthErrorHandler();

  /**
   * 包装API调用，自动处理认证错误
   */
  const withErrorHandling = useCallback(
    <T>(apiCall: () => Promise<T>) => {
      return apiCall().catch((error) => {
        handleApiError(error);
        throw error; // 重新抛出错误，让调用方可以进行额外处理
      });
    },
    [handleApiError]
  );

  return {
    withErrorHandling,
    handleApiError,
    isAuthError,
    requiresReLogin,
    getAuthErrorMessage,
  };
};

export default useAuthErrorHandler;
