import { AuthStatusCode, AuthResponseResult } from '@/types';
import { removeAccessToken } from './api';

/**
 * 认证错误处理工具类
 * 根据不同的认证状态码执行相应的操作
 */
export class AuthErrorHandler {
  
  /**
   * 处理认证错误
   * @param authResponse 认证响应结果
   * @param showNotification 是否显示通知的回调函数
   */
  static handleAuthError(
    authResponse: AuthResponseResult,
    showNotification?: (message: string, type: 'error' | 'warning' | 'info') => void
  ): void {
    const { authCode, authMessage, requireReLogin } = authResponse;

    switch (authCode) {
      case AuthStatusCode.TOKEN_INVALID:
        this.handleTokenInvalid(authMessage, showNotification);
        break;
      
      case AuthStatusCode.TOKEN_EXPIRED:
        this.handleTokenExpired(authMessage, showNotification);
        break;
      
      case AuthStatusCode.SESSION_INVALID:
        this.handleSessionInvalid(authMessage, showNotification);
        break;
      
      case AuthStatusCode.TOKEN_MISSING:
      case AuthStatusCode.NOT_LOGGED_IN:
        this.handleNotLoggedIn(authMessage, showNotification);
        break;
      
      case AuthStatusCode.TOKEN_FORMAT_ERROR:
        this.handleTokenFormatError(authMessage, showNotification);
        break;
      
      case AuthStatusCode.PERMISSION_DENIED:
        this.handlePermissionDenied(authMessage, showNotification);
        break;
      
      case AuthStatusCode.ACCOUNT_DISABLED:
        this.handleAccountDisabled(authMessage, showNotification);
        break;
      
      case AuthStatusCode.AUTH_SERVICE_ERROR:
        this.handleServiceError(authMessage, showNotification);
        break;
      
      default:
        this.handleGenericAuthError(authMessage, showNotification);
        break;
    }

    // 如果需要重新登录，执行登出操作
    if (requireReLogin) {
      this.performLogout();
    }
  }

  /**
   * Token无效处理
   */
  private static handleTokenInvalid(
    message: string,
    showNotification?: (message: string, type: 'error' | 'warning' | 'info') => void
  ): void {
    console.warn('Token无效:', message);
    showNotification?.(message || 'Token无效，请重新登录', 'warning');
  }

  /**
   * Token过期处理
   */
  private static handleTokenExpired(
    message: string,
    showNotification?: (message: string, type: 'error' | 'warning' | 'info') => void
  ): void {
    console.warn('Token过期:', message);
    showNotification?.(message || 'Token已过期，请重新登录', 'warning');
  }

  /**
   * 会话失效处理
   */
  private static handleSessionInvalid(
    message: string,
    showNotification?: (message: string, type: 'error' | 'warning' | 'info') => void
  ): void {
    console.warn('会话失效:', message);
    showNotification?.(message || '会话已失效，请重新登录', 'warning');
  }

  /**
   * 未登录处理
   */
  private static handleNotLoggedIn(
    message: string,
    showNotification?: (message: string, type: 'error' | 'warning' | 'info') => void
  ): void {
    console.warn('未登录:', message);
    showNotification?.(message || '请先登录', 'info');
  }

  /**
   * Token格式错误处理
   */
  private static handleTokenFormatError(
    message: string,
    showNotification?: (message: string, type: 'error' | 'warning' | 'info') => void
  ): void {
    console.warn('Token格式错误:', message);
    showNotification?.(message || 'Token格式错误，请重新登录', 'error');
  }

  /**
   * 权限不足处理
   */
  private static handlePermissionDenied(
    message: string,
    showNotification?: (message: string, type: 'error' | 'warning' | 'info') => void
  ): void {
    console.warn('权限不足:', message);
    showNotification?.(message || '权限不足，无法访问该资源', 'error');
  }

  /**
   * 账户被禁用处理
   */
  private static handleAccountDisabled(
    message: string,
    showNotification?: (message: string, type: 'error' | 'warning' | 'info') => void
  ): void {
    console.warn('账户被禁用:', message);
    showNotification?.(message || '账户已被禁用，请联系管理员', 'error');
  }

  /**
   * 认证服务异常处理
   */
  private static handleServiceError(
    message: string,
    showNotification?: (message: string, type: 'error' | 'warning' | 'info') => void
  ): void {
    console.error('认证服务异常:', message);
    showNotification?.(message || '认证服务异常，请稍后重试', 'error');
  }

  /**
   * 通用认证错误处理
   */
  private static handleGenericAuthError(
    message: string,
    showNotification?: (message: string, type: 'error' | 'warning' | 'info') => void
  ): void {
    console.error('认证错误:', message);
    showNotification?.(message || '认证失败，请重试', 'error');
  }

  /**
   * 执行登出操作
   */
  private static performLogout(): void {
    // 清除本地存储的token
    removeAccessToken();
    
    // 延迟跳转，确保通知能够显示
    setTimeout(() => {
      if (typeof window !== 'undefined') {
        // 跳转到登录页面
        window.location.href = '/auth/login';
      }
    }, 1500);
  }

  /**
   * 检查响应是否包含认证错误
   * @param response API响应
   * @returns 是否为认证错误
   */
  static isAuthError(response: any): boolean {
    if (!response || !response.data) {
      return false;
    }

    // 检查是否为认证响应结构
    const authResponse = response.data as AuthResponseResult;
    return (
      typeof authResponse.authCode === 'number' &&
      typeof authResponse.authMessage === 'string' &&
      typeof authResponse.requireReLogin === 'boolean' &&
      typeof authResponse.authSuccess === 'boolean'
    );
  }

  /**
   * 从API响应中提取认证错误信息
   * @param response API响应
   * @returns 认证响应结果或null
   */
  static extractAuthError(response: any): AuthResponseResult | null {
    if (this.isAuthError(response)) {
      return response.data as AuthResponseResult;
    }
    return null;
  }

  /**
   * 判断是否需要重新登录
   * @param authCode 认证状态码
   * @returns 是否需要重新登录
   */
  static requiresReLogin(authCode: number): boolean {
    const reLoginCodes = [
      AuthStatusCode.TOKEN_INVALID,
      AuthStatusCode.TOKEN_EXPIRED,
      AuthStatusCode.SESSION_INVALID,
      AuthStatusCode.TOKEN_MISSING,
      AuthStatusCode.TOKEN_FORMAT_ERROR,
      AuthStatusCode.NOT_LOGGED_IN,
      AuthStatusCode.ACCOUNT_DISABLED,
    ];
    return reLoginCodes.includes(authCode);
  }
}

/**
 * 认证错误处理的便捷函数
 */
export const handleAuthError = AuthErrorHandler.handleAuthError.bind(AuthErrorHandler);
export const isAuthError = AuthErrorHandler.isAuthError.bind(AuthErrorHandler);
export const extractAuthError = AuthErrorHandler.extractAuthError.bind(AuthErrorHandler);
export const requiresReLogin = AuthErrorHandler.requiresReLogin.bind(AuthErrorHandler);
