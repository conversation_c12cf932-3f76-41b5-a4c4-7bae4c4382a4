import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { ApiResponse, AuthStatusCode, EnhancedApiResponse } from '@/types';
import { AuthErrorHandler } from './auth-error-handler';

// API基础配置 - 使用相对路径，通过Next.js代理转发
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || '';

// 创建axios实例
const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Token管理
const getAccessToken = (): string | null => {
  if (typeof window !== 'undefined') {
    return localStorage.getItem('access_token');
  }
  return null;
};

const setAccessToken = (token: string): void => {
  if (typeof window !== 'undefined') {
    localStorage.setItem('access_token', token);
  }
};

const removeAccessToken = (): void => {
  if (typeof window !== 'undefined') {
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
  }
};

const getRefreshToken = (): string | null => {
  if (typeof window !== 'undefined') {
    return localStorage.getItem('refresh_token');
  }
  return null;
};

const setRefreshToken = (token: string): void => {
  if (typeof window !== 'undefined') {
    localStorage.setItem('refresh_token', token);
  }
};

// 请求拦截器
apiClient.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    const token = getAccessToken();
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器 - 增强认证错误处理
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    // 检查响应中的认证错误
    if (error.response?.data) {
      const responseData = error.response.data;

      // 检查是否为认证错误响应
      if (AuthErrorHandler.isAuthError(responseData)) {
        const authError = AuthErrorHandler.extractAuthError(responseData);
        if (authError) {
          console.warn('API响应包含认证错误:', authError);

          // 如果是Token过期且有刷新Token，尝试刷新
          if (authError.authCode === AuthStatusCode.TOKEN_EXPIRED && !originalRequest._retry) {
            originalRequest._retry = true;

            try {
              const refreshToken = getRefreshToken();
              if (refreshToken) {
                const response = await axios.post('/api/web/auth/refresh-token', {
                  refreshToken,
                });

                const { accessToken, refreshToken: newRefreshToken } = response.data.data;
                setAccessToken(accessToken);
                setRefreshToken(newRefreshToken);

                // 重新发送原始请求
                originalRequest.headers.Authorization = `Bearer ${accessToken}`;
                return apiClient(originalRequest);
              }
            } catch (refreshError) {
              console.error('刷新Token失败:', refreshError);
              // 刷新失败，执行认证错误处理
              AuthErrorHandler.handleAuthError(authError);
              return Promise.reject(refreshError);
            }
          }

          // 其他认证错误直接处理
          if (authError.requireReLogin) {
            AuthErrorHandler.handleAuthError(authError);
          }
        }
      }
    }

    // 兼容旧的401错误处理
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        const refreshToken = getRefreshToken();
        if (refreshToken) {
          const response = await axios.post('/api/web/auth/refresh-token', {
            refreshToken,
          });

          const { accessToken, refreshToken: newRefreshToken } = response.data.data;
          setAccessToken(accessToken);
          setRefreshToken(newRefreshToken);

          // 重新发送原始请求
          originalRequest.headers.Authorization = `Bearer ${accessToken}`;
          return apiClient(originalRequest);
        }
      } catch (refreshError) {
        // 刷新token失败，清除所有token并跳转到登录页
        removeAccessToken();
        if (typeof window !== 'undefined') {
          window.location.href = '/auth/login';
        }
        return Promise.reject(refreshError);
      }
    }

    return Promise.reject(error);
  }
);

// API响应包装器 - 适配后端JsonResult格式，支持认证错误处理
const handleApiResponse = <T>(response: AxiosResponse<any>): T => {
  const { data } = response;

  // 后端使用JsonResult格式: {code: 200, message: "success", data: {...}}
  if (data.code === 200) {
    return data.data;
  } else {
    // 检查是否为认证错误
    if (AuthErrorHandler.isAuthError(data)) {
      const authError = AuthErrorHandler.extractAuthError(data);
      if (authError) {
        // 处理认证错误，但不显示通知（由调用方决定）
        AuthErrorHandler.handleAuthError(authError);

        // 创建包含认证信息的错误
        const error = new Error(authError.authMessage || '认证失败') as any;
        error.authCode = authError.authCode;
        error.requireReLogin = authError.requireReLogin;
        error.authResponse = authError;
        throw error;
      }
    }

    // 处理特定的认证状态码
    if (data.code >= 4000 && data.code < 5000) {
      const error = new Error(data.message || '认证失败') as any;
      error.authCode = data.code;
      error.requireReLogin = AuthErrorHandler.requiresReLogin(data.code);
      throw error;
    }

    throw new Error(data.message || '请求失败');
  }
};

// 通用API方法
export const api = {
  get: async <T>(url: string, config?: AxiosRequestConfig): Promise<T> => {
    const response = await apiClient.get(url, config);
    return handleApiResponse(response);
  },

  post: async <T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
    const response = await apiClient.post(url, data, config);
    return handleApiResponse(response);
  },

  put: async <T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
    const response = await apiClient.put(url, data, config);
    return handleApiResponse(response);
  },

  delete: async <T>(url: string, config?: AxiosRequestConfig): Promise<T> => {
    const response = await apiClient.delete(url, config);
    return handleApiResponse(response);
  },

  upload: async <T>(url: string, formData: FormData, config?: AxiosRequestConfig): Promise<T> => {
    const response = await apiClient.post(url, formData, {
      ...config,
      headers: {
        'Content-Type': 'multipart/form-data',
        ...config?.headers,
      },
    });
    return handleApiResponse(response);
  },
};

// 导出token管理方法
export {
  getAccessToken,
  setAccessToken,
  removeAccessToken,
  getRefreshToken,
  setRefreshToken,
};

export default apiClient;