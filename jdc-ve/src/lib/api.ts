import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { ApiResponse } from '@/types';

// API基础配置 - 使用相对路径，通过Next.js代理转发
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || '';

// 创建axios实例
const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Token管理
const getAccessToken = (): string | null => {
  if (typeof window !== 'undefined') {
    return localStorage.getItem('access_token');
  }
  return null;
};

const setAccessToken = (token: string): void => {
  if (typeof window !== 'undefined') {
    localStorage.setItem('access_token', token);
  }
};

const removeAccessToken = (): void => {
  if (typeof window !== 'undefined') {
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
  }
};

const getRefreshToken = (): string | null => {
  if (typeof window !== 'undefined') {
    return localStorage.getItem('refresh_token');
  }
  return null;
};

const setRefreshToken = (token: string): void => {
  if (typeof window !== 'undefined') {
    localStorage.setItem('refresh_token', token);
  }
};

// 请求拦截器
apiClient.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    const token = getAccessToken();
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器 - 统一处理认证状态码
apiClient.interceptors.response.use(
  async (response: AxiosResponse) => {
    // 检查成功响应中的业务状态码
    const responseData = response.data;
    if (responseData && responseData.code) {
      const authCode = responseData.code;
      const originalRequest = response.config;

      // 处理认证相关的业务状态码
      switch (authCode) {
        case 4002: // TOKEN_EXPIRED - Token过期
          if (!originalRequest._retry) {
            originalRequest._retry = true;

            try {
              const refreshToken = getRefreshToken();
              if (refreshToken) {
                console.log('Token过期，尝试刷新...');
                const refreshResponse = await axios.post('/api/web/auth/refresh-token', {
                  refreshToken,
                });

                const { accessToken, refreshToken: newRefreshToken } = refreshResponse.data.data;
                setAccessToken(accessToken);
                setRefreshToken(newRefreshToken);

                // 重新发送原始请求
                originalRequest.headers.Authorization = `Bearer ${accessToken}`;
                return apiClient(originalRequest);
              }
            } catch (refreshError) {
              console.error('刷新Token失败:', refreshError);
              handleAuthError(authCode, responseData.message || 'Token已过期，请重新登录');
              return Promise.reject(new Error(responseData.message || 'Token已过期'));
            }
          }
          handleAuthError(authCode, responseData.message || 'Token已过期，请重新登录');
          return Promise.reject(new Error(responseData.message || 'Token已过期'));

        case 4001: // TOKEN_INVALID - Token无效
        case 4003: // SESSION_INVALID - 会话失效
        case 4004: // TOKEN_MISSING - 未提供Token
        case 4005: // TOKEN_FORMAT_ERROR - Token格式错误
        case 4006: // NOT_LOGGED_IN - 未登录
        case 4007: // ACCOUNT_DISABLED - 账户被禁用
          console.warn(`认证错误 [${authCode}]:`, responseData.message);
          handleAuthError(authCode, responseData.message || '认证失败，请重新登录');
          return Promise.reject(new Error(responseData.message || '认证失败'));

        case 403: // PERMISSION_DENIED - 权限不足
          console.warn('权限不足:', responseData.message);
          showErrorMessage(responseData.message || '权限不足，无法访问该资源');
          return Promise.reject(new Error(responseData.message || '权限不足'));

        case 401: // 兼容旧的401错误处理
          if (!originalRequest._retry) {
            originalRequest._retry = true;

            try {
              const refreshToken = getRefreshToken();
              if (refreshToken) {
                const refreshResponse = await axios.post('/api/web/auth/refresh-token', {
                  refreshToken,
                });

                const { accessToken, refreshToken: newRefreshToken } = refreshResponse.data.data;
                setAccessToken(accessToken);
                setRefreshToken(newRefreshToken);

                // 重新发送原始请求
                originalRequest.headers.Authorization = `Bearer ${accessToken}`;
                return apiClient(originalRequest);
              }
            } catch (refreshError) {
              handleAuthError(401, '认证失败，请重新登录');
              return Promise.reject(new Error('认证失败'));
            }
          }
          handleAuthError(401, '认证失败，请重新登录');
          return Promise.reject(new Error('认证失败'));
      }
    }

    return response;
  },
  async (error) => {
    // 处理HTTP错误状态码
    if (error.response?.status === 401) {
      const originalRequest = error.config;
      if (!originalRequest._retry) {
        originalRequest._retry = true;

        try {
          const refreshToken = getRefreshToken();
          if (refreshToken) {
            const response = await axios.post('/api/web/auth/refresh-token', {
              refreshToken,
            });

            const { accessToken, refreshToken: newRefreshToken } = response.data.data;
            setAccessToken(accessToken);
            setRefreshToken(newRefreshToken);

            // 重新发送原始请求
            originalRequest.headers.Authorization = `Bearer ${accessToken}`;
            return apiClient(originalRequest);
          }
        } catch (refreshError) {
          handleAuthError(401, '认证失败，请重新登录');
          return Promise.reject(refreshError);
        }
      }
      handleAuthError(401, '认证失败，请重新登录');
    }

    return Promise.reject(error);
  }
);

/**
 * 处理认证错误的统一函数
 * @param authCode 认证状态码
 * @param message 错误消息
 */
const handleAuthError = (authCode: number, message: string) => {
  console.warn(`认证错误 [${authCode}]:`, message);

  // 显示错误提示
  showErrorMessage(message);

  // 清除本地token
  removeAccessToken();

  // 延迟跳转到登录页面，确保用户能看到错误提示
  setTimeout(() => {
    if (typeof window !== 'undefined') {
      window.location.href = '/auth/login';
    }
  }, 1500);
};

/**
 * 显示错误消息的统一函数
 * @param message 错误消息
 */
const showErrorMessage = (message: string) => {
  console.error('错误提示:', message);

  // 显示用户可见的错误提示
  if (typeof window !== 'undefined') {
    // 使用alert确保用户能看到错误信息
    alert(message);

    // 如果项目使用了其他通知组件，可以替换为：
    // toast.error(message);
    // notification.error({ message });
  }
};

// API响应包装器 - 适配后端JsonResult格式
const handleApiResponse = <T>(response: AxiosResponse<any>): T => {
  const { data } = response;
  // 后端使用JsonResult格式: {code: 200, message: "success", data: {...}}
  if (data.code === 200) {
    return data.data;
  } else {
    throw new Error(data.message || '请求失败');
  }
};

// 通用API方法
export const api = {
  get: async <T>(url: string, config?: AxiosRequestConfig): Promise<T> => {
    const response = await apiClient.get(url, config);
    return handleApiResponse(response);
  },

  post: async <T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
    const response = await apiClient.post(url, data, config);
    return handleApiResponse(response);
  },

  put: async <T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
    const response = await apiClient.put(url, data, config);
    return handleApiResponse(response);
  },

  delete: async <T>(url: string, config?: AxiosRequestConfig): Promise<T> => {
    const response = await apiClient.delete(url, config);
    return handleApiResponse(response);
  },

  upload: async <T>(url: string, formData: FormData, config?: AxiosRequestConfig): Promise<T> => {
    const response = await apiClient.post(url, formData, {
      ...config,
      headers: {
        'Content-Type': 'multipart/form-data',
        ...config?.headers,
      },
    });
    return handleApiResponse(response);
  },
};

// 导出token管理方法
export {
  getAccessToken,
  setAccessToken,
  removeAccessToken,
  getRefreshToken,
  setRefreshToken,
};

export default apiClient;