// 用户相关类型定义
export interface User {
  id: number;
  username: string;
  email: string;
  nickname?: string;
  avatarUrl?: string;
  phone?: string;
  userType: 1 | 2 | 3; // 1:普通用户 2:VIP用户 3:企业用户
  status: 0 | 1 | 2; // 0:禁用 1:正常 2:待验证
  emailVerified: boolean;
  lastLoginTime?: string;
  createTime: string;
}

export interface UserProfile {
  id: number;
  username: string;
  email: string;
  nickname?: string;
  avatarUrl?: string;
  phone?: string;
  gender: 0 | 1 | 2; // 0:未知 1:男 2:女
  birthday?: string;
  country?: string;
  province?: string;
  city?: string;
}

export interface UserPoints {
  id: number;
  userId: number;
  totalPoints: number;
  availablePoints: number;
  lockedPoints: number;
  consumedPoints: number;
}

// 认证相关类型定义
export interface LoginCredentials {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface RegisterData {
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
  verificationCode: string;
}

export interface AuthResponse {
  user: User;
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

// 视频生成相关类型定义
export interface VideoGenerationTask {
  id: number;
  taskNo: string;
  userId: number;
  externalTaskId?: string;
  prompt: string;
  enhancedPrompt?: string;
  model: string;
  enhancePrompt: boolean;
  enableUpsample: boolean;
  inputImages?: string[];
  aspectRatio?: string;
  duration?: number;
  status: 1 | 2 | 3 | 4 | 5; // 1:待处理 2:处理中 3:成功 4:失败 5:取消
  progress: number;
  errorMessage?: string;
  videoUrl?: string;
  videoMediaId?: string;
  thumbnailUrl?: string;
  fileSize?: number;
  videoWidth?: number;
  videoHeight?: number;
  videoFormat: string;
  pointsCost: number;
  startTime?: string;
  endTime?: string;
  createTime: string;
}

export interface GenerateVideoParams {
  prompt: string;
  model: 'veo3'; // 只支持veo3模型
  enhancePrompt?: boolean;
  enableUpsample?: boolean;
  images?: File[];
  aspectRatio?: string;
  duration?: number;
}

// API响应类型定义
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
  success: boolean;
}

export interface PaginationParams {
  page: number;
  pageSize: number;
}

export interface PaginatedResponse<T> {
  list: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// 表单验证类型定义
export interface FormErrors {
  [key: string]: string | undefined;
}

// 通用状态类型定义
export interface LoadingState {
  isLoading: boolean;
  error?: string;
}

// 邮箱验证类型定义
export interface EmailVerification {
  email: string;
  verificationCode: string;
  verificationType: 1 | 2 | 3; // 1:注册验证 2:密码重置 3:邮箱变更
}